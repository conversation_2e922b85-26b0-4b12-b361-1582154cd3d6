import React, { useState, useRef, useEffect } from 'react';
import { Send, BarChart3, MessageCircle } from 'lucide-react';
// 导入 react-markdown 和 remark-gfm 插件
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
// 从 './types' 导入我们统一的 Message 和 AiData 类型
import { Message, AiData } from './types';
import { queryAPI, fetchSuggestionsAPI } from './services/api';
import { DataVisualization } from './components/DataVisualization';
import { TestVisualization } from './components/TestVisualization';
import userAvatar from './public/user.png';
import systemAvatar from './public/system.png';

/**
 * 防抖Hook: 延迟处理快速变化的输入值，以优化性能。
 * @param value 需要防抖的状态值
 * @param delay 延迟的毫秒数
 * @returns 防抖处理后的值
 */
function useDebounce(value: string, delay: number): string {
  const [debouncedValue, setDebouncedValue] = useState(value);
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    // 在下一次effect运行或组件卸载时清除上一个定时器
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);
  return debouncedValue;
}

/**
 * 主应用组件
 */
function App() {
  // 使用从 types.ts 导入的统一的 Message 类型
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [showTestVisualization, setShowTestVisualization] = useState(false);
  const [expandedSummaries, setExpandedSummaries] = useState<Set<string>>(new Set());
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // --- 输入联想功能的状态 (保持不变) ---
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [recommendations, setRecommendations] = useState<string[]>([]);
  const debouncedInputValue = useDebounce(inputValue, 300);

  // --- Effects (保持不变) ---
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  useEffect(() => {
    if (debouncedInputValue.trim()) {
      fetchSuggestionsAPI(debouncedInputValue).then(data => {
        setSuggestions(data);
        setShowSuggestions(data.length > 0);
      });
    } else {
      setShowSuggestions(false);
    }
  }, [debouncedInputValue]);

  // --- 事件处理器 ---

  /**
   * 切换数据洞察总结的展开/折叠状态
   */
  const toggleSummaryExpansion = (messageId: string) => {
    setExpandedSummaries(prev => {
      const newSet = new Set(prev);
      if (newSet.has(messageId)) {
        newSet.delete(messageId);
      } else {
        newSet.add(messageId);
      }
      return newSet;
    });
  };

  /**
   * 发送消息的核心函数
   */
  const handleSendMessage = async (messageContent?: string) => {
    const content = (messageContent || inputValue).trim();
    if (!content) return;

    setShowSuggestions(false);
    setRecommendations([]);
    setInputValue('');
    setIsTyping(true);

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: content,
      timestamp: new Date(),
    };

    const assistantMessageId = (Date.now() + 1).toString();
    const assistantMessageShell: Message = {
      id: assistantMessageId,
      type: 'assistant',
      content: '', // 初始化“计划”文本
      finalSummary: '', // 初始化“总结”文本
      timestamp: new Date(),
      data: { question: content, isFinalSummaryStarted: false },
    };

    setMessages(prev => [...prev, userMessage, assistantMessageShell]);

    let isFinalSummaryStage = false;

    await queryAPI(content, {
      onSummaryChunk: (chunk) => {
        setMessages(prev => prev.map(msg => {
          if (msg.id === assistantMessageId) {
            if (isFinalSummaryStage) {
              return { ...msg, finalSummary: (msg.finalSummary || '') + chunk };
            } else {
              return { ...msg, content: msg.content + chunk };
            }
          }
          return msg;
        }));
      },
      onSqlGenerated: (sql) => {
        setMessages(prev => prev.map(msg =>
            msg.id === assistantMessageId ? { ...msg, data: { ...msg.data, sql_query: sql } } : msg
        ));
      },
      onSummary: (summary) => {
        setMessages(prev => prev.map(msg => {
          if (msg.id === assistantMessageId) {
            if (isFinalSummaryStage) {
              // 这是最终总结阶段，累积到finalSummary
              return { ...msg, finalSummary: (msg.finalSummary || '') + summary };
            } else {
              // 这是初始回答阶段，累积到content
              return { ...msg, content: msg.content + summary };
            }
          }
          return msg;
        }));
      },
      onDataFinal: (dataWithMetadata) => {
        setMessages(prev => prev.map(msg =>
            msg.id === assistantMessageId ? {
              ...msg,
              data: {
                ...msg.data,
                raw_data: dataWithMetadata.raw_data,
                metadata: dataWithMetadata.metadata
              }
            } : msg
        ));
      },
      onFinalSummaryStart: () => {
        isFinalSummaryStage = true;
        setMessages(prev => prev.map(msg =>
            msg.id === assistantMessageId ? { ...msg, data: { ...msg.data, isFinalSummaryStarted: true } } : msg
        ));
      },
      onDone: () => {
        setIsTyping(false);
      },
      onRecommendations: (recs) => {
        setRecommendations(recs);
      },
      onError: (errorData) => {
        setMessages(prev => prev.map(msg =>
            msg.id === assistantMessageId
                ? { ...msg, data: { ...msg.data, error: errorData.error || '未知错误' } }
                : msg
        ));
        setIsTyping(false);
      },
    });
  };

  const handleKeyPress = (e: React.KeyboardEvent) => { if (e.key === 'Enter' && !e.shiftKey) { e.preventDefault(); handleSendMessage(); } };
  const handleSuggestionClick = (suggestion: string) => { handleSendMessage(suggestion); };
  const handleInputBlur = () => { setTimeout(() => { setShowSuggestions(false); }, 150); };
  const handleRecommendationClick = (recommendation: string) => {
    setRecommendations([]);
    handleSendMessage(recommendation);
  };

  // --- JSX 渲染 ---
  return (
      <div className="min-h-screen bg-gray-50 flex flex-col">
        <header className="bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <MessageCircle className="w-5 h-5 text-white" />
            </div>
            <h1 className="text-xl font-semibold text-gray-800">Keo ChatBI</h1>
          </div>
          <button
            onClick={() => setShowTestVisualization(!showTestVisualization)}
            className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors"
          >
            {showTestVisualization ? '隐藏测试' : '测试可视化'}
          </button>
        </header>

        <div className="flex-1 overflow-y-auto px-4 py-6 pb-32">
          <div className="max-w-2xl mx-auto space-y-6">
            {showTestVisualization && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <TestVisualization />
              </div>
            )}

            {messages.length === 0 && !showTestVisualization && (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <BarChart3 className="w-8 h-8 text-blue-500" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-800 mb-2">欢迎使用Keo ChatBI</h3>
                </div>
            )}

            {messages.map((message) => (
                <div key={message.id} className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                  <div className={`flex items-start space-x-3 ${message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                    <div className="w-8 h-8 flex-shrink-0">
                      <img src={message.type === 'user' ? userAvatar : systemAvatar} alt={`${message.type} Avatar`} className={`w-full h-full rounded-full object-cover ${message.type === 'assistant' ? 'border-2 border-blue-500' : ''}`} />
                    </div>
                    <div className={`max-w-[596px] flex-1 ${message.type === 'user' ? 'text-right' : ''}`}>
                      {/* 渲染第一部分（计划）文本 */}
                      <div className={`inline-block px-4 py-3 rounded-2xl`} style={{
                        backgroundColor: message.type === 'user' ? '#3B82F6' : '#FFFFFF',
                        color: message.type === 'user' ? '#FFFFFF' : '#1F2937',
                        border: message.type === 'user' ? 'none' : '1px solid #E5E7EB'
                      }}>
                        <p className="text-sm leading-relaxed" style={{whiteSpace: 'pre-wrap'}}>
                          {message.content === '' && !message.finalSummary && isTyping && message.type === 'assistant' ? (
                              <span className="animate-pulse">▍</span>) : (message.content)}
                        </p>
                      </div>

                      {/* 渲染数据可视化组件 */}
                      {(message.data?.raw_data && message.data.raw_data.length > 0 && message.data.metadata) && (
                          <DataVisualization
                              data={{
                                raw_data: message.data.raw_data,
                                metadata: message.data.metadata
                              }}
                              question={message.data.question || ''}
                              sqlQuery={message.data.sql_query || ''}
                          />
                      )}

                      {/* 渲染数据洞察总结 - 可折叠形式 */}
                      {message.data?.isFinalSummaryStarted && (message.finalSummary || (isTyping && message.id === messages[messages.length - 1].id)) && (
                          <div className="mt-4 bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">
                            {/* 可点击的标题栏 */}
                            <div
                                className="cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50 transition-all duration-200 px-4 py-3 bg-gray-50 border-b border-gray-200 flex items-center justify-between"
                                onClick={() => toggleSummaryExpansion(message.id)}
                            >
                              <span className="flex items-center">
                                <svg
                                    className={`w-4 h-4 mr-2 text-gray-500 transition-transform duration-200 ${expandedSummaries.has(message.id) ? 'rotate-90' : ''}`}
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7"/>
                                </svg>
                                数据洞察总结
                              </span>
                              <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                                {expandedSummaries.has(message.id) ? '收起' : '展开'}
                              </span>
                            </div>

                            {/* 可展开的内容区域 */}
                            {expandedSummaries.has(message.id) && (
                                <div className="p-4 bg-white">
                                  <div className="prose prose-sm max-w-none text-gray-800">
                                    <ReactMarkdown
                                        remarkPlugins={[remarkGfm]}
                                        components={{
                                          ol: ({node, ...props}) => <ol
                                              className="list-decimal list-inside space-y-1" {...props} />,
                                          ul: ({node, ...props}) => <ul
                                              className="list-disc list-inside space-y-1" {...props} />,
                                          p: ({node, ...props}) => <p
                                              className="mb-3 last:mb-0 leading-relaxed" {...props} />,
                                          h1: ({node, ...props}) => <h1
                                              className="text-lg font-semibold mb-3 text-gray-900" {...props} />,
                                          h2: ({node, ...props}) => <h2
                                              className="text-base font-semibold mb-2 text-gray-900" {...props} />,
                                          h3: ({node, ...props}) => <h3
                                              className="text-sm font-semibold mb-2 text-gray-900" {...props} />,
                                          strong: ({node, ...props}) => <strong
                                              className="font-semibold text-gray-900" {...props} />,
                                          li: ({node, ...props}) => <li className="mb-1" {...props} />,
                                        }}
                                    >
                                      {message.finalSummary || ''}
                                    </ReactMarkdown>
                                    {(!message.finalSummary || message.finalSummary === '') && isTyping && message.id === messages[messages.length - 1].id && (
                                        <div className="flex items-center">
                                          <span className="animate-pulse text-blue-500">▍</span>
                                          <span className="ml-2 text-xs text-gray-500">正在生成洞察总结...</span>
                                        </div>
                                    )}
                                  </div>
                                </div>
                            )}
                          </div>
                      )}

                      {message.data?.error && (
                          <div className="mt-4 bg-red-50 border border-red-200 rounded-lg p-4 text-left"><p
                              className="text-sm text-red-800"><strong>错误：</strong> {message.data.error}</p></div>)}



                      <div className="mt-2 text-xs text-gray-500">{message.timestamp.toLocaleString('zh-CN')}</div>
                    </div>
                  </div>
                </div>
            ))}

            <div ref={messagesEndRef} />
            
            {/* 推荐问题 */}
            {recommendations.length > 0 && !isTyping && (
              <div className="max-w-2xl mx-auto px-4 mt-6">
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 flex-shrink-0"></div>
                  <div className="flex-1">
                    <div className="text-sm font-medium text-gray-700 mb-3">继续探索：</div>
                    <div className="flex flex-col items-start space-y-2">
                      {recommendations.map((question, index) => (
                        <div className="inline-block" key={index}>
                          <button
                            onClick={() => handleRecommendationClick(question)}
                            className="inline-block text-left bg-gray-50 hover:bg-gray-100 border border-gray-200 rounded-full px-4 py-2 text-sm text-gray-700 transition-all duration-200 hover:translate-x-1 hover:shadow-sm"
                          >
                            {question}
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-4 shadow-lg">
          <div className="max-w-2xl mx-auto">
            <div className="flex items-start space-x-3">
              <div className="flex-1 relative">
                {showSuggestions && suggestions.length > 0 && (
                    <div className="absolute bottom-full left-0 right-0 w-full mb-2 bg-white border border-gray-200 rounded-lg shadow-xl z-10 max-h-60 overflow-y-auto">
                      <ul>
                        {suggestions.map((item, index) => (
                            <li key={index} className="border-b border-gray-100 last:border-b-0">
                              <button onMouseDown={() => handleSuggestionClick(item)} className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors">
                                {item}
                              </button>
                            </li>
                        ))}
                      </ul>
                    </div>
                )}
                <textarea
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    onKeyPress={handleKeyPress}
                    onFocus={() => { if (suggestions.length > 0 && inputValue) { setShowSuggestions(true); } }}
                    onBlur={handleInputBlur}
                    autoComplete="off"
                    placeholder="请输入您的数据分析问题..."
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm"
                    rows={1}
                    style={{ minHeight: '44px', maxHeight: '120px' }}
                    disabled={isTyping}
                />
              </div>
              <button
                  onClick={() => handleSendMessage()}
                  disabled={!inputValue.trim() || isTyping}
                  className="w-11 h-11 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded-lg transition-colors duration-200 flex-shrink-0 flex items-center justify-center"
              >
                <Send className="w-4 h-4" />
              </button>
            </div>
            <div className="mt-2 text-xs text-gray-500 text-center">
              {isTyping ? '正在分析您的问题...' : '输入问题后按回车发送'}
            </div>
          </div>
        </div>
      </div>
  );
}

export default App;
