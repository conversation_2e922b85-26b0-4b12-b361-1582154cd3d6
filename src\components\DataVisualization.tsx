import React, { useState, useMemo } from 'react';
import ReactECharts from 'echarts-for-react';
import { Bar<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Table, Scatter<PERSON><PERSON> as Scatt<PERSON>, <PERSON> } from 'lucide-react';
import { format } from 'sql-formatter';
import { ChartType } from '../types';
import { detectChartType, generateChartOptions } from '../utils/chartUtils';

interface DataVisualizationProps {
    data: {
        raw_data: (string | number)[][];
        metadata: {
            has_bitwise_fields: boolean;
            bitwise_fields: any[];
            row_count: number;
            columns: string[];
            pivot_data?: {
                rows: Array<{ cells: Array<{ value: string }> }>;
                columns: Array<{ cells: Array<{ value: string; dataType?: string }> }>;
                values: Array<Array<{ v: any }>>;
            };
        };
    };
    question: string;
    sqlQuery: string;
}

const chartTypeIcons = {
    table: Table,
    bar: BarChart3,
    line: Line<PERSON><PERSON>,
    pie: <PERSON><PERSON><PERSON>,
    scatter: <PERSON><PERSON><PERSON>,
};

const chartTypeNames = {
    table: '表格',
    bar: '柱状图',
    line: '折线图',
    pie: '饼图',
    scatter: '散点图',
};

export const DataVisualization: React.FC<DataVisualizationProps> = ({
                                                                        data,
                                                                        question,
                                                                        sqlQuery
                                                                    }) => {
    const recommendedType = useMemo(() => detectChartType(data), [data]);
    const [selectedType, setSelectedType] = useState<ChartType>(recommendedType);

    const chartOptions = useMemo(() => {
        if (selectedType === 'table') return null;
        return generateChartOptions(data, selectedType);
    }, [data, selectedType]);

    const headers = useMemo(() => {
        if (!data || !data.raw_data || data.raw_data.length === 0) {
            return [];
        }

        // 优先使用 metadata 中的列名
        if (data.metadata && data.metadata.columns && data.metadata.columns.length > 0) {
            return data.metadata.columns;
        }

        // 如果没有列名，根据第一行数据生成默认列名
        const firstRow = data.raw_data[0];
        if (!Array.isArray(firstRow)) return [];
        return firstRow.map((_, index) => `列${index + 1}`);
    }, [data]);

    const formattedSql = useMemo(() => {
        if (!sqlQuery || sqlQuery === '未能提取 SQL') return sqlQuery;

        try {
            return format(sqlQuery, {
                language: 'mysql',
                tabWidth: 2,
                keywordCase: 'upper',
                identifierCase: 'lower',
                functionCase: 'upper'
            });
        } catch (error) {
            // 如果格式化失败，返回原始SQL
            return sqlQuery;
        }
    }, [sqlQuery]);

    const renderTable = () => {
        // 计算表格的最大高度，根据数据行数动态调整
        const maxHeight = data.raw_data.length > 15 ? 'max-h-96' : data.raw_data.length > 8 ? 'max-h-80' : 'max-h-64';

        return (
            <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm">
                {/* 固定表头 */}
                <div className="bg-gray-50 border-b border-gray-200 sticky top-0 z-10">
                    <div className="overflow-hidden">
                        <table className="w-full table-fixed">
                            <thead>
                            <tr>
                                {headers.map((header, idx) => (
                                    <th key={idx} className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200 last:border-r-0">
                                        <div className="truncate" title={header}>
                                            {header}
                                        </div>
                                    </th>
                                ))}
                            </tr>
                            </thead>
                        </table>
                    </div>
                </div>

                {/* 可滚动表体 */}
                <div className={`overflow-auto ${maxHeight} bg-white`}>
                    <table className="w-full table-fixed">
                        <tbody className="divide-y divide-gray-200">
                        {data.raw_data.map((row, idx) => (
                            <tr key={idx} className="hover:bg-gray-50 transition-colors">
                                {Array.isArray(row) ? row.map((cell, cellIdx) => (
                                    <td key={cellIdx} className="px-4 py-3 text-sm text-gray-900 border-r border-gray-100 last:border-r-0">
                                        <div className="truncate" title={typeof cell === 'number' ? cell.toLocaleString() : String(cell)}>
                                            {typeof cell === 'number' ? cell.toLocaleString() : String(cell)}
                                        </div>
                                    </td>
                                )) : (
                                    <td className="px-4 py-3 text-sm text-gray-900" colSpan={headers.length}>
                                        <div className="truncate" title={String(row)}>
                                            {String(row)}
                                        </div>
                                    </td>
                                )}
                            </tr>
                        ))}
                        </tbody>
                    </table>
                </div>

                {/* 数据统计信息 */}
                <div className="px-4 py-2 bg-gray-50 border-t border-gray-200 text-xs text-gray-500 flex justify-between items-center">
                    <span>共 {data.raw_data.length} 行数据</span>
                    {data.raw_data.length > 8 && (
                        <span className="text-blue-600">
                            <svg className="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            可滚动查看
                        </span>
                    )}
                </div>
            </div>
        );
    };

    const renderChart = () => {
        if (!chartOptions) {
            return (
                <div className="flex items-center justify-center h-64 text-gray-500">
                    <div className="text-center">
                        <Clock className="w-8 h-8 mx-auto mb-2" />
                        <p>无法为当前数据生成{chartTypeNames[selectedType]}</p>
                    </div>
                </div>
            );
        }

        // 根据图表类型调整高度
        const chartHeight = selectedType === 'pie' ? '500px' : '400px';

        return (
            <div className="w-full overflow-hidden">
                <ReactECharts

                    key={selectedType}
                    notMerge={true}
                    lazyUpdate={true}

                    option={chartOptions}
                    style={{ height: chartHeight, width: '100%' }}
                    opts={{ renderer: 'canvas' }}
                />
            </div>
        );
    };

    if (!data || !data.raw_data || data.raw_data.length === 0) {
        return (
            <div className="mt-4 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="text-center text-gray-500">
                    <Clock className="w-8 h-8 mx-auto mb-2" />
                    <p>暂无数据</p>
                </div>
            </div>
        );
    }

    return (
        <div className="mt-4 bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div className="px-4 py-3 border-b border-gray-200 flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">数据可视化</span>
                <div className="flex items-center space-x-2">
                    {Object.entries(chartTypeIcons).map(([type, Icon]) => (
                        <button
                            key={type}
                            onClick={() => setSelectedType(type as ChartType)}
                            className={`p-1 hover:bg-gray-100 rounded transition-colors ${
                                selectedType === type ? 'bg-blue-50' : ''
                            }`}
                            title={chartTypeNames[type as ChartType]}
                        >
                            <Icon className={`w-4 h-4 ${
                                selectedType === type ? 'text-blue-500' : 'text-gray-500'
                            }`} />
                        </button>
                    ))}
                </div>
            </div>

            <div className="p-4">
                {selectedType === 'table' ? renderTable() : renderChart()}
            </div>

            {sqlQuery && sqlQuery !== '未能提取 SQL' && (
                <div className="px-4 py-3 bg-gray-50 border-t border-gray-200">
                    <details className="group">
                        <summary className="cursor-pointer text-sm text-gray-600 hover:text-gray-800 transition-colors">
                            查看SQL查询语句
                        </summary>
                        <div className="mt-2 bg-gray-800 rounded overflow-hidden">
              <pre className="text-xs text-green-400 p-3 overflow-x-auto whitespace-pre-wrap break-words">
                {formattedSql}
              </pre>
                        </div>
                    </details>
                </div>
            )}
        </div>
    );
};
