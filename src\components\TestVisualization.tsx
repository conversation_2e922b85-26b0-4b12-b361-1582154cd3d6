import React from 'react';
import { DataVisualization } from './DataVisualization';

// 测试数据集合 - 不同的数据格式
const testDataSets = {
  // 职称数据：基于你提供的真实后端数据格式
  realBackendData: {
    "metadata": {
      "has_bitwise_fields": false,
      "bitwise_fields": [],
      "row_count": 13,
      "columns": ["职称", "数量"],
      "pivot_data": {
        "rows": [
          {"cells": [{"value": "副教授"}]},
          {"cells": [{"value": "教授"}]},
          {"cells": [{"value": "空值"}]},
          {"cells": [{"value": "其他正高级职称"}]},
          {"cells": [{"value": "高级工程师"}]},
          {"cells": [{"value": "助理教授"}]},
          {"cells": [{"value": "研究员"}]},
          {"cells": [{"value": "正高级工程师"}]},
          {"cells": [{"value": "副研究员"}]},
          {"cells": [{"value": "助理研究员"}]},
          {"cells": [{"value": "学生"}]},
          {"cells": [{"value": "讲师"}]},
          {"cells": [{"value": "其他高级职称"}]}
        ],
        "columns": [
          {"cells": [{"value": "数量", "dataType": "number"}]}
        ],
        "values": [
          [{"v": 3847}],
          [{"v": 3709}],
          [{"v": 1044}],
          [{"v": 550}],
          [{"v": 463}],
          [{"v": 331}],
          [{"v": 261}],
          [{"v": 148}],
          [{"v": 94}],
          [{"v": 71}],
          [{"v": 20}],
          [{"v": 17}],
          [{"v": 2}]
        ]
      }
    }
  },

  // 两列数据：时间序列（保持向后兼容的格式）
  twoColumns: {
    "metadata": {
      "has_bitwise_fields": false,
      "bitwise_fields": [],
      "row_count": 7,
      "columns": ["month", "new_expert_count"],
      "pivot_data": {
        "rows": [
          {"cells": [{"value": "2025-01"}]},
          {"cells": [{"value": "2025-02"}]},
          {"cells": [{"value": "2025-03"}]},
          {"cells": [{"value": "2025-04"}]},
          {"cells": [{"value": "2025-05"}]},
          {"cells": [{"value": "2025-06"}]},
          {"cells": [{"value": "2025-07"}]}
        ],
        "columns": [
          {"cells": [{"value": "new_expert_count", "dataType": "number"}]}
        ],
        "values": [
          [{"v": 6}],
          [{"v": 3}],
          [{"v": 14}],
          [{"v": 15}],
          [{"v": 5}],
          [{"v": 32}],
          [{"v": 1}]
        ]
      }
    }
  },

  // 三列数据：散点图
  threeColumns: {
    "metadata": {
      "has_bitwise_fields": false,
      "bitwise_fields": [],
      "row_count": 6,
      "columns": ["product", "sales_volume", "satisfaction_score"],
      "pivot_data": {
        "rows": [
          {"cells": [{"value": "产品A"}]},
          {"cells": [{"value": "产品B"}]},
          {"cells": [{"value": "产品C"}]},
          {"cells": [{"value": "产品D"}]},
          {"cells": [{"value": "产品E"}]},
          {"cells": [{"value": "产品F"}]}
        ],
        "columns": [
          {"cells": [{"value": "sales_volume", "dataType": "number"}]},
          {"cells": [{"value": "satisfaction_score", "dataType": "number"}]}
        ],
        "values": [
          [{"v": 120}, {"v": 85}],
          [{"v": 95}, {"v": 92}],
          [{"v": 150}, {"v": 78}],
          [{"v": 80}, {"v": 95}],
          [{"v": 200}, {"v": 88}],
          [{"v": 110}, {"v": 90}]
        ]
      }
    }
  },

  // 多列数据：多系列图表
  multiColumns: {
    "metadata": {
      "has_bitwise_fields": false,
      "bitwise_fields": [],
      "row_count": 4,
      "columns": ["quarter", "revenue", "profit", "customer_satisfaction", "market_share"],
      "pivot_data": {
        "rows": [
          {"cells": [{"value": "Q1"}]},
          {"cells": [{"value": "Q2"}]},
          {"cells": [{"value": "Q3"}]},
          {"cells": [{"value": "Q4"}]}
        ],
        "columns": [
          {"cells": [{"value": "revenue", "dataType": "number"}]},
          {"cells": [{"value": "profit", "dataType": "number"}]},
          {"cells": [{"value": "customer_satisfaction", "dataType": "number"}]},
          {"cells": [{"value": "market_share", "dataType": "number"}]}
        ],
        "values": [
          [{"v": 120}, {"v": 95}, {"v": 88}, {"v": 76}],
          [{"v": 135}, {"v": 102}, {"v": 92}, {"v": 81}],
          [{"v": 148}, {"v": 88}, {"v": 95}, {"v": 85}],
          [{"v": 162}, {"v": 110}, {"v": 89}, {"v": 79}]
        ]
      }
    }
  }
};

export const TestVisualization: React.FC = () => {
  const [currentDataSet, setCurrentDataSet] = React.useState<keyof typeof testDataSets>('realBackendData');

  const dataSetInfo = {
    realBackendData: {
      title: "真实后端数据 - 职称统计",
      description: "基于你提供的真实后端数据格式，职称 + 数量，适合饼图、柱状图",
      question: "专家职称分布统计",
      sqlQuery: "SELECT title, COUNT(*) as count FROM experts GROUP BY title ORDER BY count DESC"
    },
    twoColumns: {
      title: "两列数据 - 时间序列",
      description: "月份 + 专家数量，适合折线图、柱状图、饼图",
      question: "每月新专家数量统计",
      sqlQuery: "SELECT month, new_expert_count FROM expert_stats WHERE year = 2025"
    },
    threeColumns: {
      title: "三列数据 - 散点图",
      description: "产品 + 销量 + 满意度，适合散点图分析",
      question: "产品销量与满意度关系分析",
      sqlQuery: "SELECT product, sales_volume, satisfaction_score FROM product_analysis"
    },
    multiColumns: {
      title: "多列数据 - 多系列图表",
      description: "季度 + 多个指标，适合多系列柱状图、折线图",
      question: "季度业务指标对比分析",
      sqlQuery: "SELECT quarter, revenue, profit, customer_satisfaction, market_share FROM quarterly_report"
    }
  };

  return (
    <div className="p-4 space-y-6">
      <div>
        <h2 className="text-xl font-bold mb-4">多列数据可视化测试</h2>

        {/* 数据集选择器 */}
        <div className="mb-4">
          <div className="flex space-x-2 mb-2">
            {Object.entries(dataSetInfo).map(([key, info]) => (
              <button
                key={key}
                onClick={() => setCurrentDataSet(key as keyof typeof testDataSets)}
                className={`px-3 py-2 text-sm rounded-lg transition-colors ${
                  currentDataSet === key
                    ? 'bg-blue-100 text-blue-700 border border-blue-300'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {info.title}
              </button>
            ))}
          </div>
          <p className="text-sm text-gray-600">
            {dataSetInfo[currentDataSet].description}
          </p>
        </div>
      </div>

      {/* 数据可视化组件 */}
      <DataVisualization
        data={testDataSets[currentDataSet]}
        question={dataSetInfo[currentDataSet].question}
        sqlQuery={dataSetInfo[currentDataSet].sqlQuery}
      />
    </div>
  );
};
