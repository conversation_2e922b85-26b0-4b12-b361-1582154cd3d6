import React from 'react';
import { DataVisualization } from './DataVisualization';

// 测试数据 - 基于你提供的新数据格式
const testData = {
  "raw_data": [
    ["2025-01", 6], 
    ["2025-02", 3], 
    ["2025-03", 14], 
    ["2025-04", 15], 
    ["2025-05", 5], 
    ["2025-06", 32], 
    ["2025-07", 1]
  ] as (string | number)[][], 
  "metadata": {
    "has_bitwise_fields": false, 
    "bitwise_fields": [], 
    "row_count": 7, 
    "columns": ["month", "new_expert_count"], 
    "pivot_data": {
      "rows": [
        {"cells": [{"value": "2025-01"}]}, 
        {"cells": [{"value": "2025-02"}]}, 
        {"cells": [{"value": "2025-03"}]}, 
        {"cells": [{"value": "2025-04"}]}, 
        {"cells": [{"value": "2025-05"}]}, 
        {"cells": [{"value": "2025-06"}]}, 
        {"cells": [{"value": "2025-07"}]}
      ], 
      "columns": [
        {"cells": [{"value": "new_expert_count", "dataType": "number"}]}
      ], 
      "values": [
        [{"v": 6}], 
        [{"v": 3}], 
        [{"v": 14}], 
        [{"v": 15}], 
        [{"v": 5}], 
        [{"v": 32}], 
        [{"v": 1}]
      ]
    }
  }
};

export const TestVisualization: React.FC = () => {
  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">数据可视化测试</h2>
      <DataVisualization 
        data={testData}
        question="每月新专家数量统计"
        sqlQuery="SELECT month, new_expert_count FROM expert_stats WHERE year = 2025"
      />
    </div>
  );
};
