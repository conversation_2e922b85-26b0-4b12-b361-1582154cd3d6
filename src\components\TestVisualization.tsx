import React from 'react';
import { DataVisualization } from './DataVisualization';

// 测试数据集合 - 不同的数据格式
const testDataSets = {
  // 两列数据：时间序列
  twoColumns: {
    "raw_data": [
      ["2025-01", 6],
      ["2025-02", 3],
      ["2025-03", 14],
      ["2025-04", 15],
      ["2025-05", 5],
      ["2025-06", 32],
      ["2025-07", 1]
    ] as (string | number)[][],
    "metadata": {
      "has_bitwise_fields": false,
      "bitwise_fields": [],
      "row_count": 7,
      "columns": ["month", "new_expert_count"],
      "pivot_data": {
        "rows": [
          {"cells": [{"value": "2025-01"}]},
          {"cells": [{"value": "2025-02"}]},
          {"cells": [{"value": "2025-03"}]},
          {"cells": [{"value": "2025-04"}]},
          {"cells": [{"value": "2025-05"}]},
          {"cells": [{"value": "2025-06"}]},
          {"cells": [{"value": "2025-07"}]}
        ],
        "columns": [
          {"cells": [{"value": "new_expert_count", "dataType": "number"}]}
        ],
        "values": [
          [{"v": 6}],
          [{"v": 3}],
          [{"v": 14}],
          [{"v": 15}],
          [{"v": 5}],
          [{"v": 32}],
          [{"v": 1}]
        ]
      }
    }
  },

  // 三列数据：散点图
  threeColumns: {
    "raw_data": [
      ["产品A", 120, 85],
      ["产品B", 95, 92],
      ["产品C", 150, 78],
      ["产品D", 80, 95],
      ["产品E", 200, 88],
      ["产品F", 110, 90]
    ] as (string | number)[][],
    "metadata": {
      "has_bitwise_fields": false,
      "bitwise_fields": [],
      "row_count": 6,
      "columns": ["product", "sales_volume", "satisfaction_score"],
      "pivot_data": {
        "rows": [],
        "columns": [],
        "values": []
      }
    }
  },

  // 多列数据：多系列图表
  multiColumns: {
    "raw_data": [
      ["Q1", 120, 95, 88, 76],
      ["Q2", 135, 102, 92, 81],
      ["Q3", 148, 88, 95, 85],
      ["Q4", 162, 110, 89, 79]
    ] as (string | number)[][],
    "metadata": {
      "has_bitwise_fields": false,
      "bitwise_fields": [],
      "row_count": 4,
      "columns": ["quarter", "revenue", "profit", "customer_satisfaction", "market_share"],
      "pivot_data": {
        "rows": [],
        "columns": [],
        "values": []
      }
    }
  }
};

export const TestVisualization: React.FC = () => {
  const [currentDataSet, setCurrentDataSet] = React.useState<keyof typeof testDataSets>('twoColumns');

  const dataSetInfo = {
    twoColumns: {
      title: "两列数据 - 时间序列",
      description: "月份 + 专家数量，适合折线图、柱状图、饼图",
      question: "每月新专家数量统计",
      sqlQuery: "SELECT month, new_expert_count FROM expert_stats WHERE year = 2025"
    },
    threeColumns: {
      title: "三列数据 - 散点图",
      description: "产品 + 销量 + 满意度，适合散点图分析",
      question: "产品销量与满意度关系分析",
      sqlQuery: "SELECT product, sales_volume, satisfaction_score FROM product_analysis"
    },
    multiColumns: {
      title: "多列数据 - 多系列图表",
      description: "季度 + 多个指标，适合多系列柱状图、折线图",
      question: "季度业务指标对比分析",
      sqlQuery: "SELECT quarter, revenue, profit, customer_satisfaction, market_share FROM quarterly_report"
    }
  };

  return (
    <div className="p-4 space-y-6">
      <div>
        <h2 className="text-xl font-bold mb-4">多列数据可视化测试</h2>

        {/* 数据集选择器 */}
        <div className="mb-4">
          <div className="flex space-x-2 mb-2">
            {Object.entries(dataSetInfo).map(([key, info]) => (
              <button
                key={key}
                onClick={() => setCurrentDataSet(key as keyof typeof testDataSets)}
                className={`px-3 py-2 text-sm rounded-lg transition-colors ${
                  currentDataSet === key
                    ? 'bg-blue-100 text-blue-700 border border-blue-300'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {info.title}
              </button>
            ))}
          </div>
          <p className="text-sm text-gray-600">
            {dataSetInfo[currentDataSet].description}
          </p>
        </div>
      </div>

      {/* 数据可视化组件 */}
      <DataVisualization
        data={testDataSets[currentDataSet]}
        question={dataSetInfo[currentDataSet].question}
        sqlQuery={dataSetInfo[currentDataSet].sqlQuery}
      />
    </div>
  );
};
