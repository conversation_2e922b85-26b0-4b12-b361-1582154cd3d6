package cn.ais.erp.main.domain.expert;

import cn.ais.erp.main.model.Context;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR> (<EMAIL>)
 * @description:
 * @date 2024/5/24 13:59
 */
@Getter
@Setter
public class Expert {

    private int id;

    @Schema(description = "姓名")
    private String surname;

    @Schema(description = "国际区号")
    private int countryCode;

    @Schema(description = "手机")
    private String phone;

    @Schema(description = "邮箱")
    private String email;

    private int sex;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "生日")
    private String birthday;

    @Schema(description = "最高学历")
    private int education;

    @Schema(description = "职称")
    private int professional;

    @Schema(description = "职务")
    private String position;

    @Schema(description = "单位/机构")
    private String organization;

    @Schema(description = "院系部门")
    private String department;

    @Schema(description = "领域")
    private int domain;

    @Schema(description = "研究方向")
    private String direction;

    private int country;

    private int province;

    private int city;

    @Schema(description = "通讯地址")
    private String address;

    @Schema(description = "电话")
    private String tel;

    @Schema(description = "备用联系")
    private String contact;

    @Schema(description = "微信号")
    private String wx;

    @Schema(description = "个人主页")
    private String url;

    @Schema(description = "简历")
    private String resume;

    @Schema(description = "h值类型")
    private int indexType;

    @Schema(description = "h值")
    private int indexValue;

    @Schema(description = "初始专家类型 4 审稿专家 8 编译专家 16 课程导师 32 会议嘉宾  64  头条创作者 ")
    private int officer;

    @Schema(description = "专家头衔 1 院士  2 国家级高层次人才 4 国家级青年人才 8 IEEE Fellow 16 ACM Fellow  32 ieee高级会员 64 ACM高级会员 128 ieee会员  256 acm会员  ")
    private int title;

    @Schema(description = "专家标签")
    private String tags;


    @Schema(description = "合作意愿2.0")
    private int purpose;

    @Schema(description = "用户id")
    private int userId;

    @Schema(description = "用户账号")
    private String account;

    @Schema(description = "开发状态 1 未开发  2 待启动  3 开发中 4 审核中 5 开发成功 6 开发失败 7开发异常  8 待补充")
    private int status;

    @Schema(description = "级别")
    private int level;

    @Schema(description = "排序")
    private int sort;

    @Schema(description = "收藏数")
    private int favorite;

    @Schema(description = "工单数")
    private int work;

    @Schema(description = "评分")
    private int score;

    @Schema(description = "扩展属性")
    private int attribute;

    @Schema(description = "来源 1 艾思专家 2智库专家 3论文作者  4导师数据库  5专家总库新增/导入 ")
    private int pf;

    @Schema(description = "专家开发渠道(对应字典aisExpertChannel)")
    private int channel;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "主要对接人")
    private int master;

    @Schema(description = "保护人")
    private int protector;

    @Schema(description = "开发人")
    private int developer;

    @Schema(description = "开发时间")
    private int developTime;


    @Schema(description = "上次合作日期")
    private int workTime;

    @Schema(description = "创建人")
    private int creator;

    @Schema(description = "操作人")
    private int operator;

    private int createTime;

    @Schema(description = "最近操作时间")
    private int updateTime;

    private boolean isFollower(int current) {//584 康杰
        return creator == current || developer == current || master == current || operator == current;
    }


    public boolean isCannotFollow(int current) {
        boolean ok = isFollower(current);
        ok = ok || Context.getExpertService().isSupportMaster(current);
        ok = ok || Context.getAdminService().isHaveRole(current, 1, 2, 3, 4, 63);
        ok = ok || Context.getExpertService().followers(id).contains(current);
        return !ok;
    }

    public boolean isNeedApply(int current) {

        // 创建人、管理人、开发人
        boolean noNeedApply = isFollower(current);
        noNeedApply = noNeedApply || Context.getExpertService().isSupportMaster(current);
        // 无保护人或保护人是当前操作人
        noNeedApply = noNeedApply || protector == 0 || protector == current;
        // 领导角色
        noNeedApply = noNeedApply || Context.getAdminService().isHaveRole(current, 1, 2, 3, 4, 20, 63);

        return !noNeedApply;

    }

    public boolean isEndCooperation() {
        String s = this.getTags();
        if (!StringUtils.isBlank(s)) {
            return Arrays.stream(s.split(",")).map(String::trim).anyMatch("74"::equals);
        }
        return false;
    }

}
