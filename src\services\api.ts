// src/services/api.ts

// [路径修改] 从根目录的 index.ts 导入类型
import { AiData } from '../types';

// const API_BASE_URL = '';

// 回调函数接口定义
interface StreamCallbacks {
  onSqlGenerated: (sql: string) => void;
  onSummaryChunk: (chunk: string) => void;
  onDataFinal: (data: {
    metadata: {
      has_bitwise_fields: boolean;
      bitwise_fields: any[];
      row_count: number;
      columns: string[];
      pivot_data: {
        rows: Array<{ cells: Array<{ value: string }> }>;
        columns: Array<{ cells: Array<{ value: string; dataType?: string }> }>;
        values: Array<Array<{ v: any }>>;
      };
    };
  }) => void;
  onFinalSummaryStart: () => void;
  onDone: () => void;
  onError: (error: AiData) => void;
  onRecommendations?: (recommendations: string[]) => void;
}

// 支持三阶段流式响应的 queryAPI 函数
const API_BASE_URL = 'http://localhost:5000';

export const queryAPI = async (
    question: string,
    callbacks: StreamCallbacks
): Promise<void> => {
  console.log('调用queryAPI，问题:', question, 'API地址:', `${API_BASE_URL}/api/query`);
  try {
    const response = await fetch(`${API_BASE_URL}/api/query`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ question }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    if (!response.body) {
      throw new Error('Response body is empty');
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';

    while (true) {
      const { done, value } = await reader.read();
      if (done) {
        break;
      }

      buffer += decoder.decode(value, { stream: true });
      const parts = buffer.split('\n\n');
      buffer = parts.pop() || '';

      for (const part of parts) {
        if (!part) continue;

        let eventName = 'message';
        let eventData = '';

        const eventLines = part.split('\n');
        for (const line of eventLines) {
          if (line.startsWith('event:')) {
            eventName = line.substring(6).trim();
          } else if (line.startsWith('data:')) {
            eventData = line.substring(5).trim();
          }
        }

        if (!eventData) continue;

        // 检查是否是结束事件的 data
        if (eventData === 'done' && eventName === 'end_stream') {
          callbacks.onDone();
          reader.cancel();
          return;
        }

        try {
          const data = JSON.parse(eventData);

          switch (eventName) {
            case 'content':
              callbacks.onSummaryChunk(data.content);
              break;
            case 'summary_chunk':
              callbacks.onSummaryChunk(data.chunk);
              break;
            case 'sql_generated':
              callbacks.onSqlGenerated(data.sql_query);
              break;
            case 'data_final':
              // 后端现在只返回metadata，不再有raw_data字段
              callbacks.onDataFinal({
                metadata: data.metadata
              });
              break;
            case 'final_summary_start':
              callbacks.onFinalSummaryStart();
              break;
            case 'error':
              callbacks.onError(data);
              break;
            case 'recommendations':
              if (callbacks.onRecommendations) {
                callbacks.onRecommendations(data.recommendations || []);
              }
              break;
          }
        } catch (parseError) {
          console.error('解析JSON失败:', eventData, parseError);
        }
      }
    }
  } catch (error) {
    console.error('API stream request failed:', error);
    const errorData = {
      error: '无法连接到后端服务',
      details: error instanceof Error ? error.message : '未知流错误',
    };
    callbacks.onError(errorData);
  }
};

// 联想建议的API
export const fetchSuggestionsAPI = async (query: string): Promise<string[]> => {
  console.log('获取建议，查询:', query, 'API地址:', `${API_BASE_URL}/api/suggestions`);
  try {
    const response = await fetch(`${API_BASE_URL}/api/suggestions`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query }),
    });
    if (!response.ok) throw new Error(`Suggestion fetch failed! status: ${response.status}`);
    const data = await response.json();
    return data.suggestions || [];
  } catch (error) {
    console.error('Suggestion API request failed:', error);
    return [];
  }
};
