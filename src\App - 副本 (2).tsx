import React, { useState, useRef, useEffect } from 'react';
import { Send, BarChart3, MessageCircle } from 'lucide-react';
import { Message } from './types';
// +++ 修改：从 api 服务中导入新增的 fetchSuggestionsAPI 函数 +++
import { queryAPI, fetchSuggestionsAPI } from './services/api';
import { DataVisualization } from './components/DataVisualization';
import userAvatar from './public/user.png';
import systemAvatar from './public/system.png';

// +++ 新增：防抖 (Debounce) Hook +++
// 这是一个独立的辅助函数，用于优化性能，避免因用户快速输入而频繁请求API。
// 将它直接放在这里，您就无需创建新文件。
function useDebounce(value: string, delay: number): string {
  const [debouncedValue, setDebouncedValue] = useState(value);
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    // 在下一次 effect 运行前或组件卸载时，清除上一个定时器
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);
  return debouncedValue;
}

function App() {
  // --- 您原有的 State，保持不变 ---
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // +++ 新增：用于输入联想功能的 State +++
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  // 使用防抖 Hook，延迟300毫秒处理输入值
  const debouncedInputValue = useDebounce(inputValue, 300);

  // --- 您原有的滚动逻辑，保持不变 ---
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // +++ 新增：通过 Effect Hook 获取输入建议 +++
  useEffect(() => {
    // 仅当防抖后的输入值不为空时，才去获取建议
    if (debouncedInputValue.trim()) {
      fetchSuggestionsAPI(debouncedInputValue).then(data => {
        setSuggestions(data);
        // 只有当有建议返回时，才决定显示建议框
        setShowSuggestions(data.length > 0);
      });
    } else {
      // 如果输入框为空，则隐藏建议框
      setShowSuggestions(false);
    }
  }, [debouncedInputValue]);


  // --- 修改：让 handleSendMessage 可以接收一个可选参数，方便从建议项直接调用 ---
  const handleSendMessage = async (messageContent?: string) => {
    // 如果提供了 messageContent（来自建议项），则使用它，否则使用输入框的当前值
    const content = (messageContent || inputValue).trim();
    if (!content) return;

    // 发送消息时，总是隐藏建议框
    setShowSuggestions(false);

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: content,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    const currentQuestion = content; // 使用最终确定的内容
    setInputValue(''); // 清空输入框
    setIsTyping(true);

    try {
      const response = await queryAPI(currentQuestion);

      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: response.summary,
        timestamp: new Date(),
        data: response,
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: `抱歉，处理您的请求时出现了错误：${error instanceof Error ? error.message : '未知错误'}`,
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsTyping(false);
    }
  };

  // --- 您原有的回车发送逻辑，保持不变 ---
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // +++ 新增：处理建议项点击事件的函数 +++
  const handleSuggestionClick = (suggestion: string) => {
    // 直接将建议作为消息发送
    handleSendMessage(suggestion);
  };

  // +++ 新增：处理输入框失焦事件的函数，用于隐藏建议列表 +++
  const handleInputBlur = () => {
    // 延迟隐藏，以确保点击建议项的事件能被正确触发
    setTimeout(() => {
      setShowSuggestions(false);
    }, 150);
  };

  // --- 从这里开始是 JSX 渲染部分 ---
  return (
      <div className="min-h-screen bg-gray-50 flex flex-col">
        {/* Header - 您原有的代码，保持不变 */}
        <header className="bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <MessageCircle className="w-5 h-5 text-white" />
            </div>
            <h1 className="text-xl font-semibold text-gray-800">Keo ChatBI</h1>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
            <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
            <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
          </div>
        </header>

        {/* Chat Messages - 您原有的代码，保持不变 */}
        <div className="flex-1 overflow-y-auto px-4 py-6 pb-32">
          <div className="max-w-2xl mx-auto space-y-6">
            {messages.length === 0 && (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <BarChart3 className="w-8 h-8 text-blue-500" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-800 mb-2">欢迎使用Keo ChatBI</h3>
                  <p className="text-gray-600 max-w-md mx-auto">
                    您可以用自然语言询问数据相关问题，我会为您生成相应的分析结果和可视化图表。
                  </p>
                  <div className="mt-6 flex flex-wrap justify-center gap-2">
                    <button
                        onClick={() => setInputValue('有多少专家？')}
                        className="px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-full text-sm text-gray-700 transition-colors"
                    >
                      有多少专家？
                    </button>
                    <button
                        onClick={() => setInputValue('按等级统计专家数量')}
                        className="px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-full text-sm text-gray-700 transition-colors"
                    >
                      按等级统计专家数量
                    </button>
                    <button
                        onClick={() => setInputValue('列出各个职称专家的数量分布')}
                        className="px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-full text-sm text-gray-700 transition-colors"
                    >
                      列出各个职称专家的数量分布
                    </button>
                  </div>
                </div>
            )}

            {messages.map((message) => (
                <div key={message.id} className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                  <div className={`w-full ${message.type === 'user' ? 'order-2' : 'order-1'}`}>
                    <div className={`flex items-start space-x-3 ${message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                      <div className="w-8 h-8 flex-shrink-0">
                        <img
                            src={message.type === 'user' ? userAvatar : systemAvatar}
                            alt={message.type === 'user' ? 'User Avatar' : 'System Avatar'}
                            className={`w-full h-full rounded-full object-cover ${
                                message.type === 'user' ? '' : 'border-2 border-blue-500'
                            }`}
                        />
                      </div>
                      <div className={`flex-1 ${message.type === 'user' ? 'text-right' : ''}`}>
                        <div className={`inline-block px-4 py-3 rounded-2xl ${
                            message.type === 'user'
                                ? 'bg-blue-500 text-white'
                                : 'bg-white border border-gray-200 text-gray-800'
                        }`}>
                          <p className="text-sm leading-relaxed">{message.content}</p>
                        </div>
                        {message.data && message.data.raw_data && message.data.raw_data.length > 0 && (
                            <DataVisualization
                                data={message.data.raw_data}
                                question={message.data.question}
                                sqlQuery={message.data.sql_query}
                            />
                        )}
                        {message.data && message.data.error && (
                            <div className="mt-4 bg-red-50 border border-red-200 rounded-lg p-4">
                              <p className="text-sm text-red-800">
                                <strong>错误：</strong> {message.data.error}
                              </p>
                            </div>
                        )}
                        <div className="mt-2 text-xs text-gray-500">
                          {message.timestamp.toLocaleString('zh-CN')}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
            ))}

            {isTyping && (
                <div className="flex justify-start">
                  <div className="w-full">
                    <div className="flex items-start space-x-3">
                      <div className="w-8 h-8 flex-shrink-0">
                        <img
                            src={systemAvatar}
                            alt="System Avatar"
                            className="w-full h-full rounded-full object-cover border-2 border-blue-500"
                        />
                      </div>
                      <div className="flex-1">
                        <div className="inline-block px-4 py-3 rounded-2xl bg-white border border-gray-200">
                          <div className="flex space-x-1">
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
            )}

            <div ref={messagesEndRef} />
          </div>
        </div>

        {/* --- Fixed Input Area - 修改部分 --- */}
        <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-4 shadow-lg">
          <div className="max-w-2xl mx-auto">
            <div className="flex items-start space-x-3">
              <div className="flex-1 relative">
                {/* +++ 新增：联想建议框的渲染逻辑 +++ */}
                {showSuggestions && suggestions.length > 0 && (
                    <div className="absolute bottom-full left-0 right-0 w-full mb-2 bg-white border border-gray-200 rounded-lg shadow-xl z-10 max-h-60 overflow-y-auto">
                      <ul>
                        {suggestions.map((item, index) => (
                            <li key={index} className="border-b border-gray-100 last:border-b-0">
                              <button
                                  onMouseDown={() => handleSuggestionClick(item)}
                                  className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                              >
                                {item}
                              </button>
                            </li>
                        ))}
                      </ul>
                    </div>
                )}
                <textarea
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    onKeyPress={handleKeyPress}
                    // +++ 新增：事件处理器和属性 +++
                    onFocus={() => {
                      if (suggestions.length > 0 && inputValue) {
                        setShowSuggestions(true);
                      }
                    }}
                    onBlur={handleInputBlur}
                    autoComplete="off"
                    placeholder="请输入您的数据分析问题..."
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm"
                    rows={1}
                    style={{ minHeight: '44px', maxHeight: '120px' }}
                    disabled={isTyping}
                />
              </div>
              <button
                  onClick={() => handleSendMessage()}
                  disabled={!inputValue.trim() || isTyping}
                  className="w-11 h-11 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded-lg transition-colors duration-200 flex-shrink-0 flex items-center justify-center"
              >
                <Send className="w-4 h-4" />
              </button>
            </div>
            <div className="mt-2 text-xs text-gray-500 text-center">
              {isTyping ? '正在分析您的问题...' : '输入问题后按回车发送'}
            </div>
          </div>
        </div>
      </div>
  );
}

export default App;
