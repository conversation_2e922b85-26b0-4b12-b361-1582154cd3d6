import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  server: {
    host: true,
    proxy: {
      // 字符串简写写法
      // '/api': 'http://127.0.0.1:5000',
      // 或者更详细的配置
      '/api': {
        target: 'http://127.0.0.1:5000', // 你本地后端的地址
        changeOrigin: true, // 需要虚拟主机站点
        // 如果你的后端接口路径没有 /api 前缀，可以用下面这行重写
        // rewrite: (path) => path.replace(/^\/api/, ''),
      },
    },
  },
  optimizeDeps: {
    exclude: ['lucide-react'],
  },
});
