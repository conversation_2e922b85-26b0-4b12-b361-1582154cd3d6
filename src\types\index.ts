/**
 * 定义聊天界面中所有消息的核心结构。
 */
export interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string; // 对于用户，这是其提问；对于AI，这是第一阶段的“计划”文本。
  finalSummary?: string; // 对于AI，这里存储第三阶段的、基于数据的“洞察总结”。
  timestamp: Date;
  data?: AiData; // 可选对象，用于存储所有来自AI的结构化数据。
}

/**
 * 定义AI消息中 'data' 对象的数据结构。
 */
export interface AiData {
  question?: string;
  sql_query?: string;
  raw_data?: (string | number)[][];
  error?: string;
  isFinalSummaryStarted?: boolean; // 用于控制UI渲染阶段的标志位。
}

/**
 * 定义图表组件可能用到的图表类型。
 */
export type ChartType = 'table' | 'bar' | 'line' | 'pie' | 'scatter';
