// 模拟API响应，用于测试前端功能
import { AiData } from '../types';

interface StreamCallbacks {
  onSqlGenerated: (sql: string) => void;
  onSummaryChunk: (chunk: string) => void;
  onDataFinal: (data: {
    raw_data: (string | number)[][];
    metadata: {
      has_bitwise_fields: boolean;
      bitwise_fields: any[];
      row_count: number;
      columns: string[];
      pivot_data?: {
        rows: Array<{ cells: Array<{ value: string }> }>;
        columns: Array<{ cells: Array<{ value: string; dataType?: string }> }>;
        values: Array<Array<{ v: any }>>;
      };
    };
  }) => void;
  onFinalSummaryStart: () => void;
  onDone: () => void;
  onError: (error: AiData) => void;
  onRecommendations?: (recommendations: string[]) => void;
}

// 模拟数据
const mockData = {
  question: "每月销售数据分析",
  sql: "SELECT month, sales_amount, product_count FROM sales_data WHERE year = 2025",
  raw_data: [
    ["2025-01", 125000, 45],
    ["2025-02", 98000, 38],
    ["2025-03", 156000, 52],
    ["2025-04", 134000, 47],
    ["2025-05", 178000, 61],
    ["2025-06", 145000, 49]
  ],
  metadata: {
    has_bitwise_fields: false,
    bitwise_fields: [],
    row_count: 6,
    columns: ["月份", "销售额", "产品数量"],
    pivot_data: {
      rows: [
        { cells: [{ value: "2025-01" }] },
        { cells: [{ value: "2025-02" }] },
        { cells: [{ value: "2025-03" }] },
        { cells: [{ value: "2025-04" }] },
        { cells: [{ value: "2025-05" }] },
        { cells: [{ value: "2025-06" }] }
      ],
      columns: [
        { cells: [{ value: "销售额", dataType: "number" }] },
        { cells: [{ value: "产品数量", dataType: "number" }] }
      ],
      values: [
        [{ v: 125000 }, { v: 45 }],
        [{ v: 98000 }, { v: 38 }],
        [{ v: 156000 }, { v: 52 }],
        [{ v: 134000 }, { v: 47 }],
        [{ v: 178000 }, { v: 61 }],
        [{ v: 145000 }, { v: 49 }]
      ]
    }
  }
};

// 模拟流式响应
export const mockQueryAPI = async (
  question: string,
  callbacks: StreamCallbacks
): Promise<void> => {
  try {
    console.log('开始模拟API调用，问题:', question);
    
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 发送SQL查询
    callbacks.onSqlGenerated(mockData.sql);
    
    // 模拟计划阶段
    const planChunks = [
      "正在分析您的问题...",
      "\n",
      "根据问题" + question + "，我将查询销售数据",
      "\n",
      "计划：1. 查询2025年各月销售数据 2. 生成可视化图表 3. 提供数据洞察"
    ];
    
    for (const chunk of planChunks) {
      await new Promise(resolve => setTimeout(resolve, 200));
      callbacks.onSummaryChunk(chunk);
    }
    
    // 模拟数据返回
    await new Promise(resolve => setTimeout(resolve, 1500));
    callbacks.onDataFinal({
      raw_data: mockData.raw_data,
      metadata: mockData.metadata
    });
    
    // 开始最终总结
    callbacks.onFinalSummaryStart();
    
    // 模拟总结阶段
    const summaryChunks = [
      "## 数据洞察分析\n\n",
      "### 1. 销售额趋势\n",
      "2025年上半年销售额呈现波动上升趋势，从1月的12.5万元增长到5月的17.8万元，增长幅度达42.4%。\n\n",
      "### 2. 产品数量关联\n",
      "产品数量与销售额呈正相关关系，相关系数为0.89。5月产品数量达到61个，销售额也达到峰值。\n\n",
      "### 3. 关键发现\n",
      "- 2月销售额相对较低（9.8万元），可能与春节假期有关\n",
      "- 3-5月增长最为显著，建议关注这一时期的市场策略\n",
      "- 6月数据略有回落，需要持续关注后续趋势"
    ];
    
    for (const chunk of summaryChunks) {
      await new Promise(resolve => setTimeout(resolve, 300));
      callbacks.onSummaryChunk(chunk);
    }
    
    // 推荐后续问题
    if (callbacks.onRecommendations) {
      callbacks.onRecommendations([
        "分析各产品类别的销售表现",
        "对比去年同期销售数据",
        "预测下半年销售趋势"
      ]);
    }
    
    // 完成
    await new Promise(resolve => setTimeout(resolve, 500));
    callbacks.onDone();
    
    console.log('模拟API调用完成');
    
  } catch (error) {
    console.error('模拟API调用失败:', error);
    callbacks.onError({
      error: '模拟API调用失败',
      details: error instanceof Error ? error.message : '未知错误'
    });
  }
};

// 模拟建议API
export const mockFetchSuggestionsAPI = async (query: string): Promise<string[]> => {
  await new Promise(resolve => setTimeout(resolve, 300));
  const suggestions = [
    "分析每月销售趋势",
    "对比不同产品销售数据",
    "查看客户满意度统计"
  ];
  return suggestions.filter(s => s.toLowerCase().includes(query.toLowerCase()));
};