// 测试数据 - 基于你提供的新数据格式
export const testData = {
  "raw_data": [
    ["2025-01", 6], 
    ["2025-02", 3], 
    ["2025-03", 14], 
    ["2025-04", 15], 
    ["2025-05", 5], 
    ["2025-06", 32], 
    ["2025-07", 1]
  ], 
  "metadata": {
    "has_bitwise_fields": false, 
    "bitwise_fields": [], 
    "row_count": 7, 
    "columns": ["month", "new_expert_count"], 
    "pivot_data": {
      "rows": [
        {"cells": [{"value": "2025-01"}]}, 
        {"cells": [{"value": "2025-02"}]}, 
        {"cells": [{"value": "2025-03"}]}, 
        {"cells": [{"value": "2025-04"}]}, 
        {"cells": [{"value": "2025-05"}]}, 
        {"cells": [{"value": "2025-06"}]}, 
        {"cells": [{"value": "2025-07"}]}
      ], 
      "columns": [
        {"cells": [{"value": "new_expert_count", "dataType": "number"}]}
      ], 
      "values": [
        [{"v": 6}], 
        [{"v": 3}], 
        [{"v": 14}], 
        [{"v": 15}], 
        [{"v": 5}], 
        [{"v": 32}], 
        [{"v": 1}]
      ]
    }
  }
};

// 测试图表工具函数
import { detectChartType, generateChartOptions } from './utils/chartUtils';

console.log('检测到的图表类型:', detectChartType(testData));
console.log('柱状图配置:', generateChartOptions(testData, 'bar'));
console.log('折线图配置:', generateChartOptions(testData, 'line'));
console.log('饼图配置:', generateChartOptions(testData, 'pie'));
