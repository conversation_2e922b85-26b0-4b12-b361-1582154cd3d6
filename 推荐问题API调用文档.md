# ChatBI 推荐问题接口调用文档

## 📋 概述

本文档为前端团队提供ChatBI推荐问题的完整集成指南。系统基于最近4轮对话历史，智能生成3个相关问题，支持两种接入方式：**自动流式集成** 和 **独立API调用**。

---

## 🚀 快速开始

### 方式一：自动流式集成（推荐）

**无需额外代码**，现有SSE事件流已自动扩展推荐功能。

```javascript
// 1. 监听推荐事件（添加到现有EventSource监听器中）
eventSource.addEventListener('recommendations', function(event) {
    const data = JSON.parse(event.data);
    const recommendations = data.recommendations; // Array<string>
    
    // 2. 展示推荐问题
    displayRecommendations(recommendations);
});

// 3. 推荐问题点击处理
function onRecommendationClick(question) {
    // 直接发起新的查询
    sendMessage(question);
    hideRecommendations();
}
```

### 方式二：独立API调用

适用于需要手动触发推荐或调试场景。

```javascript
// 获取推荐问题
async function fetchRecommendations() {
    try {
        const response = await fetch('/api/recommendations', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        const data = await response.json();
        return data.recommendations; // Array<string>
    } catch (error) {
        console.error('获取推荐失败:', error);
        return []; // 返回空数组避免前端报错
    }
}
```

---

## 📡 API 端点详情

### 1. 获取推荐问题

**端点**: `POST /api/recommendations`  
**描述**: 基于当前对话历史生成3个智能推荐问题

**请求示例**:
```http
POST /api/recommendations HTTP/1.1
Host: localhost:8000
Content-Type: application/json

{} // 无参数
```

**响应示例**:
```json
{
    "recommendations": [
        "按职称统计专家数量分布",
        "查看计算机科学领域的所有专家",
        "查询最近一个月内创建的专家"
    ]
}
```

**错误处理**:
- 500错误：返回默认推荐问题
- 网络错误：前端应优雅降级

### 2. 查看记忆状态

**端点**: `GET /api/memory/status`  
**描述**: 查看当前对话记忆状态（调试用）

**响应示例**:
```json
{
    "memory_size": 3,
    "max_size": 4,
    "conversation_history": [
        {
            "user_question": "查询专家信息",
            "generated_sql": "SELECT * FROM experts...",
            "query_results_summary": "共10条记录 | 字段：姓名,职称...",
            "final_summary": "根据查询结果...",
            "data_insights": ["查询返回10条记录"],
            "timestamp": "2025-07-18T10:30:00"
        }
    ]
}
```

### 3. 清空对话记忆

**端点**: `POST /api/memory/clear`  
**描述**: 清空所有对话历史（用户切换或重置场景）

**响应示例**:
```json
{
    "status": "success",
    "message": "对话记忆已清空"
}
```

---

## 🎯 事件流集成详解

### SSE事件类型扩展

在现有事件流基础上，新增以下事件：

| 事件类型 | 触发时机 | 数据格式 |
|---------|----------|----------|
| `recommendations` | 对话总结完成后 | `{recommendations: string[]}` |
| `end_stream` | 整个流程结束 | `done` |

### 完整事件监听示例

```javascript
class ChatBIEventHandler {
    constructor(eventSource) {
        this.eventSource = eventSource;
        this.setupListeners();
    }

    setupListeners() {
        // 现有事件监听
        this.eventSource.addEventListener('plan_chunk', this.handlePlanChunk);
        this.eventSource.addEventListener('sql_generated', this.handleSqlGenerated);
        this.eventSource.addEventListener('data_final', this.handleDataFinal);
        this.eventSource.addEventListener('summary_chunk', this.handleSummaryChunk);
        
        // 新增推荐事件监听
        this.eventSource.addEventListener('recommendations', this.handleRecommendations);
        this.eventSource.addEventListener('end_stream', this.handleEndStream);
    }

    handleRecommendations = (event) => {
        const { recommendations } = JSON.parse(event.data);
        this.displayRecommendations(recommendations);
    }

    handleEndStream = () => {
        // 可以在这里添加对话结束后的清理工作
        console.log('对话流程结束');
    }

    displayRecommendations(recommendations) {
        // 将推荐问题展示在聊天界面底部
        const container = this.createRecommendationsContainer();
        container.innerHTML = '';
        
        recommendations.forEach(question => {
            const button = this.createRecommendationButton(question);
            container.appendChild(button);
        });
        
        container.style.display = 'block';
    }
}
```

---

## 🎨 前端展示规范

### 推荐问题UI组件

```html
<!-- 推荐容器 -->
<div id="recommendation-container" class="recommendation-container" style="display: none;">
    <div class="recommendation-title">继续探索：</div>
    <div class="recommendation-questions">
        <!-- 动态生成推荐问题按钮 -->
    </div>
</div>
```

### CSS样式规范

```css
.recommendation-container {
    margin-top: 20px;
    padding: 15px;
    border-top: 1px solid #e0e0e0;
    animation: slideIn 0.3s ease-out;
}

.recommendation-question {
    display: block;
    width: 100%;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 20px;
    padding: 8px 16px;
    margin: 5px 0;
    font-size: 13px;
    color: #495057;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
}

.recommendation-question:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

@keyframes slideIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}
```

---

## 🔄 状态管理

### 推荐问题生命周期

1. **对话开始** → 隐藏推荐
2. **用户提问** → 清空推荐
3. **对话结束** → 展示推荐
4. **推荐点击** → 隐藏推荐 + 发起新查询

### 状态管理代码

```javascript
class RecommendationManager {
    constructor() {
        this.recommendations = [];
        this.container = null;
    }

    showRecommendations(recommendations) {
        this.recommendations = recommendations;
        this.render();
    }

    hideRecommendations() {
        this.recommendations = [];
        this.render();
    }

    render() {
        const container = this.getContainer();
        container.style.display = this.recommendations.length > 0 ? 'block' : 'none';
        
        // 渲染推荐问题...
    }

    getContainer() {
        if (!this.container) {
            this.container = document.getElementById('recommendation-container');
        }
        return this.container;
    }
}
```

---

## 🐛 常见问题处理

### 1. 推荐为空
```javascript
// 返回空数组时的处理
if (recommendations.length === 0) {
    console.log('暂无推荐问题');
    // 可以选择隐藏推荐区域或显示提示信息
}
```

### 2. 网络错误恢复
```javascript
// 网络错误时的降级处理
async function getRecommendationsWithFallback() {
    try {
        return await fetchRecommendations();
    } catch (error) {
        console.warn('推荐服务不可用，使用默认推荐');
        return [
            '查看所有专家信息',
            '按职称统计专家',
            '查询最新专家'
        ];
    }
}
```

### 3. 重复推荐检测
```javascript
// 避免重复展示相同推荐
function deduplicateRecommendations(newRecs, existingRecs) {
    return newRecs.filter(rec => !existingRecs.includes(rec));
}
```

---

## 📊 调试工具

### 快速测试脚本

```javascript
// 浏览器控制台测试
async function testRecommendations() {
    console.log('=== 测试推荐系统 ===');
    
    // 1. 测试内存状态
    const status = await fetch('/api/memory/status').then(r => r.json());
    console.log('当前记忆状态:', status);
    
    // 2. 测试推荐API
    const recs = await fetch('/api/recommendations').then(r => r.json());
    console.log('推荐问题:', recs.recommendations);
    
    // 3. 清空记忆测试
    await fetch('/api/memory/clear', { method: 'POST' });
    console.log('记忆已清空');
}

// 运行测试
testRecommendations();
```

---

## 📞 技术支持

- **接口异常**：检查网络连接和服务器状态
- **推荐为空**：确认已有对话历史（至少完成1轮对话）
- **前端集成**：参考 `recommendations_demo.html` 完整示例
- **性能优化**：推荐结果已缓存，前端可添加本地缓存

**联系开发**：如有集成问题，请提供具体错误信息和复现步骤