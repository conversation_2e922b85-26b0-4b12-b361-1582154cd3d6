import { BackendResponse } from '../types';

const API_BASE_URL = 'http://127.0.0.1:5000';

export const queryAPI = async (question: string): Promise<BackendResponse> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/query`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ question }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('API request failed:', error);
    throw new Error('无法连接到后端服务，请检查服务是否正常运行');
  }
};
export const fetchSuggestionsAPI = async (query: string): Promise<string[]> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/suggestions`, { // 同样使用 /api 前缀
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ query }),
    });

    if (!response.ok) {
      // 如果后端返回错误，这里简单地抛出，不在UI上显示
      throw new Error(`Suggestion fetch failed! status: ${response.status}`);
    }

    const data = await response.json();
    // 确保总是返回一个数组，即使后端返回的 suggestions 是 null 或 undefined
    return data.suggestions || [];
  } catch (error) {
    console.error('Suggestion API request failed:', error);
    // 在联想功能失败时，我们不打断用户，只在控制台报错，并返回空数组
    return [];
  }
};
