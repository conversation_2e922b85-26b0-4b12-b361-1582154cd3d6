import { ChartType } from '../types';
// 定义新的数据接口，增强代码可读性

interface ChartMetadata {
  has_bitwise_fields: boolean;
  bitwise_fields: any[];
  row_count: number;
  columns: string[];
  pivot_data?: {
    rows: Array<{ cells: Array<{ value: string }> }>;
    columns: Array<{ cells: Array<{ value: string; dataType?: string }> }>;
    values: Array<Array<{ v: any }>>;
  };
}

interface ChartApiResponse {
  raw_data?: (string | number)[][];  // raw_data现在是可选的
  metadata: ChartMetadata;
}

// 辅助函数：从metadata.pivot_data获取数据
const getDisplayData = (data: ChartApiResponse) => {
  let displayData: (string | number)[][] = [];

  if (data.metadata?.pivot_data && data.metadata.pivot_data.rows.length > 0) {
    const pivotData = data.metadata.pivot_data;
    displayData = pivotData.rows.map((row, rowIndex) =>
      row.cells.map((cell, cellIndex) => {
        const valueData = pivotData.values[rowIndex]?.[cellIndex];
        return valueData?.v ?? cell.value;
      })
    );
  } else if (data.raw_data) {
    displayData = data.raw_data;
  }

  // 处理null值：将null替换为"空值"或"未知"
  return displayData.map(row =>
    row.map((cell, index) => {
      if (cell === null || cell === undefined) {
        // 如果是第一列（通常是标签列），替换为"空值"
        // 如果是数值列，替换为0
        return index === 0 ? "空值" : 0;
      }
      return cell;
    })
  );
};

// 辅助函数：分析数据结构
const analyzeDataStructure = (data: ChartApiResponse) => {
  const displayData = getDisplayData(data);
  const { metadata } = data;
  
  if (!displayData || displayData.length === 0) {
    return {
      columnCount: 0,
      rowCount: 0,
      columnTypes: [],
      numericColumns: [],
      categoryColumns: [],
      hasTimeData: false
    };
  }

  const firstRow = displayData[0];
  const columnCount = firstRow.length;
  const rowCount = displayData.length;

  // 分析每列的数据类型
  const columnTypes = firstRow.map((_, colIndex) => {
    const columnValues = displayData.map(row => row[colIndex]);
    const numericCount = columnValues.filter(val => typeof val === 'number').length;
    const stringCount = columnValues.filter(val => typeof val === 'string').length;

    return {
      index: colIndex,
      name: metadata.columns[colIndex] || `列${colIndex + 1}`,
      isNumeric: numericCount > stringCount,
      numericRatio: numericCount / columnValues.length,
      hasTimePattern: columnValues.some(val =>
        typeof val === 'string' && /\d{4}-\d{2}/.test(val)
      )
    };
  });

  const numericColumns = columnTypes.filter(col => col.isNumeric);
  const categoryColumns = columnTypes.filter(col => !col.isNumeric);

  return {
    columnCount,
    rowCount,
    columnTypes,
    numericColumns,
    categoryColumns,
    hasTimeData: columnTypes.some(col => col.hasTimePattern)
  };
};

export const detectChartType = (data: ChartApiResponse): ChartType => {
  const displayData = getDisplayData(data);
  
  if (!displayData || displayData.length === 0) {
    return 'table';
  }

  const firstRow = displayData[0];
  if (!Array.isArray(firstRow) || firstRow.length < 2) {
    return 'table';
  }

  const analysis = analyzeDataStructure(data);

  // 如果没有数值列，只能用表格
  if (analysis.numericColumns.length === 0) {
    return 'table';
  }

  // 根据列数和数据特征推荐图表类型
  if (analysis.columnCount === 2) {
    // 两列数据：类别 + 数值
    if (analysis.rowCount <= 8) {
      return 'pie'; // 少量分类数据适合饼图
    } else if (analysis.hasTimeData) {
      return 'line'; // 时间序列数据适合折线图
    } else {
      return 'bar'; // 其他情况用柱状图
    }
  } else if (analysis.columnCount === 3 && analysis.numericColumns.length >= 2) {
    // 三列数据且有两个数值列：适合散点图
    return 'scatter';
  } else if (analysis.numericColumns.length > 1) {
    // 多个数值列：适合多系列图表
    if (analysis.hasTimeData) {
      return 'line'; // 时间序列多指标用折线图
    } else {
      return 'bar'; // 其他情况用柱状图
    }
  } else {
    // 默认情况
    if (analysis.rowCount > 20) {
      return 'table'; // 大量数据建议用表格
    } else {
      return 'bar';
    }
  }
};

/**
* 根据新的数据结构生成 ECharts 配置项
* @param chartData 包含 raw_data 和 metadata 的完整 API 响应
* @param chartType 图表类型 ('bar', 'line', 'pie', etc.)
* @returns ECharts 的 option 配置对象
*/

export const generateChartOptions = (chartData: ChartApiResponse, chartType: ChartType) => {
  const displayData = getDisplayData(chartData);
  const { metadata } = chartData;
  
  if (!displayData || displayData.length === 0 || !metadata) return null;

  const firstRow = displayData[0];
  if (!Array.isArray(firstRow)) return null;

  const analysis = analyzeDataStructure(chartData);
  const headers = metadata.columns;

  // 基础配置
  const colors = ['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#06b6d4', '#84cc16', '#f97316'];

  // 提取标签列（通常是第一个非数值列）
  const labelColumnIndex = analysis.categoryColumns.length > 0 ? analysis.categoryColumns[0].index : 0;
  const labels = displayData.map(row => row[labelColumnIndex]);
  const labelHeader = headers[labelColumnIndex] || 'Category';

  switch (chartType) {
    case 'bar': {
      // 支持多系列柱状图
      const series = analysis.numericColumns.map((col, index) => ({
        name: col.name,
        type: 'bar' as const,
        data: displayData.map(row => row[col.index]),
        itemStyle: { color: colors[index % colors.length] }
      }));

      return {
        tooltip: {
          trigger: 'axis' as const,
          formatter: (params: any) => {
            let result = `${params[0].axisValue}<br/>`;
            params.forEach((param: any) => {
              result += `${param.marker}${param.seriesName}: ${typeof param.value === 'number' ? param.value.toLocaleString() : param.value}<br/>`;
            });
            return result;
          }
        },
        legend: {
          show: analysis.numericColumns.length > 1,
          top: 10,
          type: 'scroll'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category' as const,
          data: labels,
          name: labelHeader,
          nameLocation: 'middle',
          nameGap: 30,
          axisLabel: {
            rotate: labels.some(label => String(label).length > 8) ? 45 : 0,
            interval: 0
          }
        },
        yAxis: {
          type: 'value' as const,
          name: analysis.numericColumns.length === 1 ? analysis.numericColumns[0].name : '数值',
          nameLocation: 'middle',
          nameGap: 50
        },
        series
      };
    }

    case 'line': {
      // 支持多系列折线图
      const series = analysis.numericColumns.map((col, index) => ({
        name: col.name,
        type: 'line' as const,
        data: displayData.map(row => row[col.index]),
        smooth: true,
        itemStyle: { color: colors[index % colors.length] },
        lineStyle: { color: colors[index % colors.length] },
        symbol: 'circle',
        symbolSize: 6
      }));

      return {
        tooltip: {
          trigger: 'axis' as const,
          formatter: (params: any) => {
            let result = `${params[0].axisValue}<br/>`;
            params.forEach((param: any) => {
              result += `${param.marker}${param.seriesName}: ${typeof param.value === 'number' ? param.value.toLocaleString() : param.value}<br/>`;
            });
            return result;
          }
        },
        legend: {
          show: analysis.numericColumns.length > 1,
          top: 10,
          type: 'scroll'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category' as const,
          data: labels,
          name: labelHeader,
          nameLocation: 'middle',
          nameGap: 30,
          boundaryGap: false
        },
        yAxis: {
          type: 'value' as const,
          name: analysis.numericColumns.length === 1 ? analysis.numericColumns[0].name : '数值',
          nameLocation: 'middle',
          nameGap: 50
        },
        series
      };
    }

    case 'pie': {
      // 饼图只使用第一个数值列
      const valueColumn = analysis.numericColumns[0];
      if (!valueColumn) return null;

      const pieData = displayData.map((row, index) => ({
        name: String(row[labelColumnIndex]),
        value: row[valueColumn.index],
        itemStyle: { color: colors[index % colors.length] }
      }));

      return {
        tooltip: {
          trigger: 'item' as const,
          formatter: '{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'horizontal',
          bottom: '5%',
          left: 'center',
          type: 'scroll'
        },
        series: [{
          name: valueColumn.name,
          type: 'pie' as const,
          radius: ['30%', '70%'],
          center: ['50%', '45%'],
          data: pieData,
          avoidLabelOverlap: true,
          itemStyle: {
            borderRadius: 8,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: true,
            position: 'outside',
            formatter: '{b}\n{d}%',
            fontSize: 12
          },
          labelLine: { show: true },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      };
    }

    case 'scatter': {
      // 散点图需要至少两个数值列
      if (analysis.numericColumns.length < 2) {
        console.warn('Scatter plot requires at least 2 numeric columns. Chart will not be rendered.');
        return null;
      }

      const xColumn = analysis.numericColumns[0];
      const yColumn = analysis.numericColumns[1];

      const scatterData = displayData.map(row => [
        row[xColumn.index],
        row[yColumn.index],
        String(row[labelColumnIndex]) // 用于tooltip显示
      ]);

      return {
        tooltip: {
          trigger: 'item' as const,
          formatter: (params: any) => {
            const [x, y, label] = params.data;
            return `${label}<br/>${xColumn.name}: ${typeof x === 'number' ? x.toLocaleString() : x}<br/>${yColumn.name}: ${typeof y === 'number' ? y.toLocaleString() : y}`;
          }
        },
        legend: {
          show: false
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value' as const,
          name: xColumn.name,
          nameLocation: 'middle',
          nameGap: 30
        },
        yAxis: {
          type: 'value' as const,
          name: yColumn.name,
          nameLocation: 'middle',
          nameGap: 50
        },
        series: [{
          name: `${xColumn.name} vs ${yColumn.name}`,
          type: 'scatter' as const,
          data: scatterData,
          itemStyle: { color: colors[0] },
          symbolSize: 8
        }]
      };
    }

    default:
      return null;
  }
};
