import { ChartType } from '../types';
// 定义新的数据接口，增强代码可读性

interface ChartMetadata {
  has_bitwise_fields: boolean;
  bitwise_fields: any[];
  row_count: number;
  columns: string[];
  pivot_data?: {
    rows: Array<{ cells: Array<{ value: string }> }>;
    columns: Array<{ cells: Array<{ value: string; dataType?: string }> }>;
    values: Array<Array<{ v: any }>>;
  };
}

interface ChartApiResponse {
  raw_data: (string | number)[][];
  metadata: ChartMetadata;
}

export const detectChartType = (data: ChartApiResponse): ChartType => {
  if (!data || !data.raw_data || data.raw_data.length === 0) {
    return 'table';
  }

  // 根据数据特征智能推荐图表类型
  const rowCount = data.raw_data.length;
  const firstRow = data.raw_data[0];

  if (!Array.isArray(firstRow) || firstRow.length < 2) {
    return 'table';
  }

  // 检查是否有数值数据
  const hasNumericData = data.raw_data.some(row =>
    Array.isArray(row) && row.some(cell => typeof cell === 'number')
  );

  if (!hasNumericData) {
    return 'table';
  }

  // 根据数据行数推荐图表类型
  if (rowCount <= 10) {
    return 'bar'; // 少量数据适合柱状图
  } else if (rowCount <= 20) {
    return 'line'; // 中等数据量适合折线图
  } else {
    return 'table'; // 大量数据建议用表格
  }
};

/**
* 根据新的数据结构生成 ECharts 配置项
* @param chartData 包含 raw_data 和 metadata 的完整 API 响应
* @param chartType 图表类型 ('bar', 'line', 'pie', etc.)
* @returns ECharts 的 option 配置对象
*/

export const generateChartOptions = (chartData: ChartApiResponse, chartType: ChartType) => {
  // 1. 从新的数据结构中解构出所需信息
  const { raw_data, metadata } = chartData;
  if (!raw_data || raw_data.length === 0 || !metadata) return null;

  const data = raw_data;
  const headers = metadata.columns; // 例如: ["month", "new_expert_count"]

  const firstRow = data[0];
  if (!Array.isArray(firstRow)) return null;

  // 2. 提取标签和数值的逻辑保持不变 (第一列为标签，最后一列为数值)
  const labels = data.map(row => row[0]);
  const values = data.map(row => row[row.length - 1]);

  // 验证是否存在数值数据
  const numericValues = values.filter(v => typeof v === 'number');
  if (numericValues.length === 0) return null;

  // 3. 使用 headers 让图表更具描述性
  const labelHeader = headers?.[0] || 'Category';
  const valueHeader = headers?.[headers.length - 1] || 'Value';

  const baseOptions = {
    tooltip: {
      trigger: 'item' as const,
      formatter: '{b}: {c}'
    },
    legend: {
      show: true,
      bottom: 10
    }
  };

  switch (chartType) {
    case 'bar':
      return {
        ...baseOptions,
        tooltip: { trigger: 'axis' as const },
        xAxis: {
          type: 'category' as const,
          data: labels,
          name: labelHeader, // 动态设置X轴名称
          axisLabel: {
            rotate: labels.some(label => String(label).length > 6) ? 45 : 0
          }
        },
        yAxis: {
          type: 'value' as const,
          name: valueHeader // 动态设置Y轴名称
        },
        series: [{
          name: valueHeader, // 动态设置系列名称
          type: 'bar' as const,
          data: values,
          itemStyle: { color: '#3b82f6' }
        }]
      };

    case 'line':
      return {
        ...baseOptions,
        tooltip: { trigger: 'axis' as const },
        xAxis: {
          type: 'category' as const,
          data: labels,
          name: labelHeader // 动态设置X轴名称
        },
        yAxis: {
          type: 'value' as const,
          name: valueHeader // 动态设置Y轴名称
        },
        series: [{
          name: valueHeader, // 动态设置系列名称
          type: 'line' as const,
          data: values,
          smooth: true,
          itemStyle: { color: '#3b82f6' },
          lineStyle: { color: '#3b82f6' }
        }]
      };

    case 'pie':
      return {
        // 饼图的 tooltip 和 legend 配置与 baseOptions 不同，因此单独定义
        tooltip: {
          trigger: 'item' as const,
          formatter: '{b}: {c} ({d}%)'
        },
        legend: {
          // 保留你原有的详细 legend 配置
          orient: 'horizontal',
          bottom: '5%',
          left: 'center',
          type: 'scroll'
          // ...其他 legend 配置
        },
        series: [{
          name: valueHeader, // 动态设置系列名称
          type: 'pie' as const,
          radius: ['30%', '60%'],
          center: ['50%', '45%'],
          data: data.map(row => ({
            name: row[0],
            value: row[row.length - 1]
          })),
          // 保留你原有的详细 series 配置
          avoidLabelOverlap: true,
          itemStyle: {
            borderRadius: 8,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: { show: true, position: 'outside', formatter: '{b}\n{d}%' },
          labelLine: { show: true },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      };

    case 'scatter':
      // 4. 散点图需要至少两列数值数据。当前数据格式不适用。
      //    为保持健壮性，我们假设散点图需要的数据格式为 [label, xValue, yValue]。
      if (firstRow.length < 3) {
        console.warn('Scatter plot requires at least 3 columns (e.g., [label, xValue, yValue]). Chart will not be rendered.');
        return null; // 数据格式不符，不生成图表
      }

      const xHeader = headers?.[1] || 'X Value';
      const yHeader = headers?.[headers.length - 1] || 'Y Value';

      return {
        ...baseOptions,
        tooltip: {
          trigger: 'item' as const,
          formatter: (params: any) => `${params.data[2] || ''}: (${params.data[0]}, ${params.data[1]})`
        },
        xAxis: { type: 'value' as const, name: xHeader },
        yAxis: { type: 'value' as const, name: yHeader },
        series: [{
          name: `${xHeader} vs ${yHeader}`,
          type: 'scatter' as const,
          data: data.map(row => [row[1], row[row.length - 1], row[0]]),
          itemStyle: { color: '#3b82f6' }
        }]
      };

    default:
      return null;
  }
};
